"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, X, Gift, Sparkles, Clock, Star } from "lucide-react";
import FormField from "./FormField";
import { OfferIncentive } from "@/types/onboarding";

interface OfferIncentiveFormProps {
  incentive?: OfferIncentive;
  onChange: (incentive: OfferIncentive | undefined) => void;
  disabled?: boolean;
}

const INCENTIVE_TYPES = [
  { value: "discount", label: "Discount", description: "Percentage or fixed amount off" },
  { value: "bonus", label: "Bonus", description: "Extra features or credits" },
  { value: "free_trial", label: "Free Trial", description: "Extended trial period" },
  { value: "credit", label: "Credit", description: "Account credits or balance" },
  { value: "extended_plan", label: "Extended Plan", description: "Longer subscription period" },
] as const;

export default function OfferIncentiveForm({ incentive, onChange, disabled = false }: OfferIncentiveFormProps) {
  const [showForm, setShowForm] = useState(!!incentive);

  const handleAddIncentive = () => {
    setShowForm(true);
    onChange({
      type: "discount",
      headline: "",
      details: "",
      cta_label: "Claim Offer"
    });
  };

  const handleRemoveIncentive = () => {
    setShowForm(false);
    onChange(undefined);
  };

  const handleUpdateIncentive = (field: keyof OfferIncentive, value: string) => {
    if (!incentive) return;
    
    onChange({
      ...incentive,
      [field]: value
    });
  };

  if (!showForm) {
    return (
      <Card className="border-dashed border-2 border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Gift className="h-8 w-8 text-muted-foreground mb-2" />
          <CardTitle className="text-lg mb-2">Add Offer Incentive</CardTitle>
          <CardDescription className="text-center mb-4">
            Boost conversions with special offers like discounts, bonuses, or free trials
          </CardDescription>
          <Button
            type="button"
            variant="outline"
            onClick={handleAddIncentive}
            disabled={disabled}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Incentive
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-lg flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Offer Incentive
          </CardTitle>
          <CardDescription>
            Create a compelling incentive to increase conversion rates
          </CardDescription>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleRemoveIncentive}
          disabled={disabled}
          className="text-muted-foreground hover:text-foreground"
        >
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormField>
          <Label htmlFor="incentive-type" className="text-sm font-medium">Incentive Type</Label>
          <Select
            value={incentive?.type || "discount"}
            onValueChange={(value) => handleUpdateIncentive("type", value)}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select incentive type" />
            </SelectTrigger>
            <SelectContent>
              {INCENTIVE_TYPES.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  <div className="flex flex-col">
                    <span className="font-medium">{type.label}</span>
                    <span className="text-xs text-muted-foreground">{type.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FormField>

        <FormField>
          <Label htmlFor="incentive-headline" className="text-sm font-medium">Headline</Label>
          <Input
            id="incentive-headline"
            placeholder="e.g., 50% off your first $10k in payments"
            value={incentive?.headline || ""}
            onChange={(e) => handleUpdateIncentive("headline", e.target.value)}
            disabled={disabled}
            className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Keep it short and compelling (recommended: under 60 characters)
          </p>
        </FormField>

        <FormField>
          <Label htmlFor="incentive-details" className="text-sm font-medium">Details</Label>
          <Textarea
            id="incentive-details"
            placeholder="e.g., Valid for new users for the first 3 months. Auto-applied at signup."
            value={incentive?.details || ""}
            onChange={(e) => handleUpdateIncentive("details", e.target.value)}
            disabled={disabled}
            rows={3}
            className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Provide clear terms and conditions for the incentive
          </p>
        </FormField>

        <FormField>
          <Label htmlFor="incentive-cta" className="text-sm font-medium">Call-to-Action Label</Label>
          <Input
            id="incentive-cta"
            placeholder="e.g., Claim Discount, Get Bonus, Start Free Trial"
            value={incentive?.cta_label || ""}
            onChange={(e) => handleUpdateIncentive("cta_label", e.target.value)}
            disabled={disabled}
            className="transition-all duration-200 focus:ring-2 focus:ring-primary/20"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Action-oriented text for the button (recommended: under 20 characters)
          </p>
        </FormField>

        {/* Enhanced Incentive Card Preview */}
        {incentive?.headline && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6"
          >
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              Incentive Card Preview
            </h4>

            {/* Realistic Incentive Card */}
            <div className="relative overflow-hidden rounded-xl border-2 border-dashed border-primary/30 bg-gradient-to-br from-primary/5 via-background to-primary/10 p-6 shadow-lg">
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute top-4 right-4">
                  <Gift className="h-16 w-16 text-primary" />
                </div>
              </div>

              {/* Content */}
              <div className="relative z-10">
                {/* Incentive Type Badge */}
                <div className="inline-flex items-center gap-1 rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary mb-3">
                  {incentive.type === 'discount' && <Star className="h-3 w-3" />}
                  {incentive.type === 'bonus' && <Gift className="h-3 w-3" />}
                  {incentive.type === 'free_trial' && <Clock className="h-3 w-3" />}
                  {incentive.type === 'credit' && <Sparkles className="h-3 w-3" />}
                  {incentive.type === 'extended_plan' && <Clock className="h-3 w-3" />}
                  {INCENTIVE_TYPES.find(t => t.value === incentive.type)?.label || 'Special Offer'}
                </div>

                {/* Headline */}
                <h3 className="text-xl font-bold text-foreground mb-2 leading-tight">
                  {incentive.headline || "Your Amazing Offer"}
                </h3>

                {/* Details */}
                {incentive.details && (
                  <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                    {incentive.details}
                  </p>
                )}

                {/* CTA Button */}
                <div className="flex items-center gap-3">
                  <Button
                    size="lg"
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-semibold px-6 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
                  >
                    {incentive.cta_label || "Claim Offer"}
                  </Button>

                  {/* Trust Indicator */}
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>Trusted by 10k+ users</span>
                  </div>
                </div>

                {/* Powered by AdMesh */}
                <div className="mt-4 pt-3 border-t border-border/50">
                  <p className="text-xs text-muted-foreground flex items-center gap-1">
                    <Sparkles className="h-3 w-3" />
                    Powered by AdMesh
                  </p>
                </div>
              </div>
            </div>

            {/* Preview Notes */}
            <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                <strong>Preview Note:</strong> This is how your incentive will appear to users when they discover your offer through AdMesh recommendations.
              </p>
            </div>
          </motion.div>
        )}
      </CardContent>
    </Card>
  );
}
